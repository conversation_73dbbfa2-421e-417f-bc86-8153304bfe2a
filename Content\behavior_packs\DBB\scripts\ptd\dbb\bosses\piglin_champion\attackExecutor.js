import { system } from "@minecraft/server";
import { getTarget } from "../general_mechanics/targetUtils";
import { executeHorizontalAttack } from "./attacks/horizontal";
import { executeVerticalAttack } from "./attacks/vertical";
import { executeFootStompAttack } from "./attacks/foot_stomp";
import { executeSpinSlamAttack } from "./attacks/spin_slam";
import { executeBodySlamAttack } from "./attacks/body_slam";
import { executeUpchuckAttack } from "./attacks/upchuck";
import { executeChargingAttack, startContinuousChargingDamage } from "./attacks/charging";
import { executeSummoningChantAttack } from "./attacks/summoning_chant";
import { executeHealingAbility } from "./abilities/healing";
import { stopPiglinChampionSounds } from "./soundManager";
/**
 * Attack timing points in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMINGS = {
    horizontal: 38,
    vertical: 44,
    foot_stomp: 24,
    spin_slam_phase1: 83,
    spin_slam_phase2: 124,
    body_slam: 63,
    upchuck: 55,
    charging_continuous: 46, // Start continuous damage during charge movement
    charging: 96, // Final impact damage
    healing_phase1: 43,
    healing_phase2: 130,
    summoning_chant: 40,
    stunned_standing_phase1: 30, // End of damage_to_stunned
    stunned_standing_phase2: 150, // End of stunned_standing (30 + 120)
    stunned_sitting_phase1: 30, // End of damage_to_stunned
    stunned_sitting_phase2: 150 // End of stunned_sitting (30 + 120)
};
/**
 * Executes an attack using runTimeout-based timing instead of timer-based system
 * @param piglinChampion The piglin champion entity
 * @param attack The attack type to execute
 */
export function executeAttack(piglinChampion, attack) {
    try {
        // Get target for attacks that need it
        const target = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
        // Schedule attack events based on attack type
        switch (attack) {
            case "horizontal":
                scheduleHorizontalAttack(piglinChampion);
                break;
            case "vertical":
                scheduleVerticalAttack(piglinChampion, target);
                break;
            case "foot_stomp":
                scheduleFootStompAttack(piglinChampion);
                break;
            case "spin_slam":
                scheduleSpinSlamAttack(piglinChampion);
                break;
            case "body_slam":
                scheduleBodySlamAttack(piglinChampion);
                break;
            case "upchuck":
                scheduleUpchuckAttack(piglinChampion);
                break;
            case "charging":
                scheduleChargingAttack(piglinChampion);
                break;
            case "summoning_chant":
                scheduleSummoningChantAttack(piglinChampion, target);
                break;
            case "healing":
                scheduleHealingAbility(piglinChampion);
                break;
            default:
                console.warn(`Unknown attack type: ${attack}`);
                break;
        }
    }
    catch (error) {
        console.warn(`Error executing attack ${attack}: ${error}`);
    }
}
/**
 * Schedule horizontal attack events
 */
function scheduleHorizontalAttack(piglinChampion) {
    // Execute attack at timing point
    const attackTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "horizontal") {
                executeHorizontalAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout);
        }
    }, ATTACK_TIMINGS.horizontal);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "horizontal", 186, 80);
}
/**
 * Schedule vertical attack events
 */
function scheduleVerticalAttack(piglinChampion, target) {
    // Execute attack at timing point
    const attackTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "vertical") {
                // Get fresh target if needed
                const currentTarget = target || getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
                if (currentTarget) {
                    executeVerticalAttack(piglinChampion, currentTarget);
                }
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout);
        }
    }, ATTACK_TIMINGS.vertical);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "vertical", 186, 80);
}
/**
 * Schedule foot stomp attack events
 */
function scheduleFootStompAttack(piglinChampion) {
    // Execute attack at timing point
    const attackTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "foot_stomp") {
                executeFootStompAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout);
        }
    }, ATTACK_TIMINGS.foot_stomp);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "foot_stomp", 186, 80);
}
/**
 * Schedule spin slam attack events
 */
function scheduleSpinSlamAttack(piglinChampion) {
    // Execute first phase at timing point 1
    const attackTimeout1 = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout1);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "spin_slam") {
                executeSpinSlamAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout1);
        }
    }, ATTACK_TIMINGS.spin_slam_phase1);
    // Execute second phase at timing point 2
    const attackTimeout2 = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout2);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "spin_slam") {
                executeSpinSlamAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout2);
        }
    }, ATTACK_TIMINGS.spin_slam_phase2);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "spin_slam", 186, 80);
}
/**
 * Schedule body slam attack events
 */
function scheduleBodySlamAttack(piglinChampion) {
    // Execute attack at timing point
    const attackTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "body_slam") {
                executeBodySlamAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout);
        }
    }, ATTACK_TIMINGS.body_slam);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "body_slam", 186, 80);
}
/**
 * Schedule upchuck attack events
 */
function scheduleUpchuckAttack(piglinChampion) {
    // Execute attack at timing point
    const attackTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "upchuck") {
                executeUpchuckAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout);
        }
    }, ATTACK_TIMINGS.upchuck);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "upchuck", 186, 80);
}
/**
 * Schedule charging attack events
 */
function scheduleChargingAttack(piglinChampion) {
    // Start continuous damage during charge movement
    const continuousTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(continuousTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                startContinuousChargingDamage(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(continuousTimeout);
        }
    }, ATTACK_TIMINGS.charging_continuous);
    // Execute final impact at timing point
    const attackTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                executeChargingAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout);
        }
    }, ATTACK_TIMINGS.charging);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "charging", 186, 80);
}
/**
 * Schedule summoning chant attack events
 */
function scheduleSummoningChantAttack(piglinChampion, target) {
    // Execute attack at timing point
    const attackTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(attackTimeout);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "summoning_chant") {
                // Get fresh target if needed
                const currentTarget = target || getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
                executeSummoningChantAttack(piglinChampion, currentTarget);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(attackTimeout);
        }
    }, ATTACK_TIMINGS.summoning_chant);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "summoning_chant", 186, 80);
}
/**
 * Schedule healing ability events
 */
function scheduleHealingAbility(piglinChampion) {
    // Execute phase 1 (healing) at timing point 1
    const healingTimeout1 = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(healingTimeout1);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "healing") {
                executeHealingAbility(piglinChampion, 1);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(healingTimeout1);
        }
    }, ATTACK_TIMINGS.healing_phase1);
    // Execute phase 2 (knockback) at timing point 2
    const healingTimeout2 = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(healingTimeout2);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "healing") {
                executeHealingAbility(piglinChampion, 2);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(healingTimeout2);
        }
    }, ATTACK_TIMINGS.healing_phase2);
    // Schedule reset and cooldown
    scheduleAttackReset(piglinChampion, "healing", 158, 80);
}
/**
 * Schedules attack reset and cooldown for any attack
 * @param piglinChampion The piglin champion entity
 * @param attackType The attack type being reset
 * @param animationDuration Duration of the attack animation in ticks
 * @param cooldownDuration Duration of the cooldown in ticks
 */
function scheduleAttackReset(piglinChampion, attackType, animationDuration, cooldownDuration) {
    // Schedule reset when animation is complete
    const resetTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === attackType) {
                // Stop all sounds when resetting attack
                stopPiglinChampionSounds(piglinChampion);
                // Reset attack and set cooldown
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
                piglinChampion.setProperty("ptd_dbb:cooling_down", true);
                // Schedule cooldown end
                const cooldownTimeout = system.runTimeout(() => {
                    try {
                        const stillDead = piglinChampion.getProperty("ptd_dbb:dead");
                        if (!stillDead) {
                            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
                        }
                    }
                    catch (error) {
                        // Entity might have been removed
                    }
                }, cooldownDuration);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(resetTimeout);
        }
    }, animationDuration);
}
